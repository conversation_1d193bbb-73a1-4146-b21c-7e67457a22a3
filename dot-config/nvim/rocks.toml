# rocks.toml - LuaRocks-based plugin configuration for rocks.nvim

[plugins]
# Neorg - Note taking and organization system
# Using rocks.nvim to avoid circular dependency issues with vim.pack.add()
"neorg" = "9.1.1"

# Neorg telescope integration (if needed as separate rock)
# "neorg-telescope" = "*"

[plugins.neorg]
# Neorg-specific configuration can go here if needed
# This ensures proper dependency resolution through LuaRocks

[config]
# Global rocks.nvim configuration
auto_setup = true
colorscheme = "kanagawa"
